# Codebase Structure

This document provides a detailed overview of the DreamFlow application codebase organization.

**Project Name**: DreamFlow (configured as `dreamflow` in pubspec.yaml)
**Display Name**: Money Lover Chat (used in UI and main application class)
**Description**: A Flutter application for managing finances with transaction tracking, categorization, statistics, and a chat interface for transaction entry.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers for state management
- **Service Layer**: Encapsulates business logic, parsing, and data access
- **Data Layer**: Data models and persistence services

## Directory Structure

### Root Level

```
dreamflow/
├── android/          # Android-specific configuration
├── ios/              # iOS-specific configuration
├── lib/              # Main Dart code
├── assets/           # Application assets (e.g., localization files)
│   └── l10n/
│       ├── en.json
│       └── es.json
├── docs/             # Project documentation
├── scripts/          # Build and utility scripts
├── pubspec.yaml      # Dependencies and project configuration
└── ...
```

### Main Application Code (lib/)

```
lib/
├── models/           # Data models
│   ├── transaction_model.dart
│   ├── parse_result.dart
│   ├── localization_data.dart
│   ├── amount_candidate.dart
│   └── category_suggestion.dart
├── services/         # Business logic and data services
│   ├── parser/       # Hybrid ML parsing module
│   │   ├── mlkit_parser_service.dart
│   │   ├── fallback_parser_service.dart
│   │   ├── learned_association_service.dart
│   │   ├── category_finder_service.dart
│   │   ├── category_keyword_map.dart
│   │   ├── learned_category_storage.dart
│   │   ├── entity_extractor_base.dart
│   │   └── real_entity_extractor.dart
│   ├── storage_service.dart
│   ├── localization_service.dart
│   └── transaction_parser_service.dart  # Legacy parser (deprecated)
├── screens/          # UI screens
│   ├── chat_screen.dart
│   ├── categories_screen.dart
│   ├── settings_screen.dart
│   └── statistics_screen.dart
├── widgets/          # Reusable UI components
│   ├── transaction_message.dart
│   ├── category_picker_dialog.dart
│   ├── quick_reply_widget.dart
│   └── transaction_edit_dialog.dart
├── utils/            # Utility functions
│   ├── currency_utils.dart
│   ├── amount_utils.dart
│   ├── raw_number_finder.dart
│   └── startup_timer.dart
├── navigation/       # Navigation logic
│   └── app_navigation.dart
├── main.dart         # Application entry point
├── theme.dart        # App theme configuration
├── audio_recorder.dart    # Audio recording functionality
├── file_upload.dart       # File upload utilities
├── image_upload.dart      # Image handling utilities
└── video_recorder.dart    # Video recording functionality
```

## Key Components

### Models (`lib/models/`)

- **transaction_model.dart**: Contains the core data models:
  - `Transaction`: Represents a financial transaction.
  - `TransactionCategory`: Defines transaction categories.
  - `TransactionType`: An enum for `expense`, `income`, and `loan`.
  - `ChatMessage`: Represents a message in the chat.
  - `TransactionProvider`: Manages transaction and chat message state.
- **parse_result.dart**:
  - `ParseResult`: A data transfer object from the parser service to the UI.
  - `ParseStatus`: An enum (`success`, `needsCategory`, `needsType`, `failed`, etc.) indicating the result of a parsing attempt.
- **localization_data.dart**:
  - `LocalizationData`: Holds keywords and number formatting rules for a specific locale.
- **amount_candidate.dart**:
  - `AmountCandidate`: Represents a potential amount found during parsing.
- **category_suggestion.dart**:
    - `CategorySuggestion`: Represents a suggested category with a confidence score.

### Services (`lib/services/`)

- **storage_service.dart**: Manages local data persistence using `shared_preferences`.
- **localization_service.dart**: Loads and provides localization data from JSON files for the `FallbackParserService`.
- **parser/mlkit_parser_service.dart**: The primary parsing service, orchestrating a "Trust but Verify" system using ML Kit, `RawNumberFinder`, and the `LearnedAssociationService`.
- **parser/fallback_parser_service.dart**: A localizable, regex-based parser that serves as a safety net.
- **parser/learned_association_service.dart**: The app's learning system. Stores and retrieves user corrections to improve parsing speed and accuracy over time.

### Utilities (`lib/utils/`)

- **currency_utils.dart**: Provides helper functions for currency formatting and handling.
- **amount_utils.dart**: Contains utilities for amount parsing and validation.
- **raw_number_finder.dart**: A utility that extracts all raw numeric values from text, a key part of the "Trust but Verify" strategy.
- **startup_timer.dart**: A utility for measuring app startup performance.

### Screens (`lib/screens/`)

- **chat_screen.dart**: The main chat interface for entering transactions.
- **categories_screen.dart**: Allows users to manage transaction categories.
- **settings_screen.dart**: Allows users to configure app settings.
- **statistics_screen.dart**: Displays financial statistics and data visualizations.

### Widgets (`lib/widgets/`)

- **transaction_message.dart**: A widget to display a saved transaction in the chat.
- **category_picker_dialog.dart**: A dialog for manually selecting a category.
- **quick_reply_widget.dart**: A widget that displays interactive buttons in a chat message.
- **transaction_edit_dialog.dart**: A dialog for editing existing transactions.

### Multimedia Support (`lib/`)

- **audio_recorder.dart**: Handles audio recording functionality.
- **file_upload.dart**: Provides file upload utilities.
- **image_upload.dart**: Manages image capture and selection.
- **video_recorder.dart**: Handles video recording and playback.