I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The legacy `TransactionParserService` is completely unused - no imports or references exist anywhere in the active codebase, only in documentation files. The current architecture is well-designed with proper separation of concerns, dependency injection, and comprehensive testing. All 4 refinements are implemented and individually tested, but logging validation is missing from integration tests. The main gap is a comprehensive test that exercises all refinements together in realistic scenarios. Performance infrastructure exists with good baselines. Test data is sufficient for accuracy validation. The main.dart correctly uses `TransactionParsingService` and ChatScreen uses the new `PendingTransactionState` for state management.

### Approach

I'll complete the final cleanup phase by removing the legacy `TransactionParserService` that's no longer used, cleaning up any remaining legacy references, and creating comprehensive integration tests that validate all 4 refinements working together. The approach focuses on safe removal of unused code, comprehensive end-to-end testing of the complete refactored architecture, and performance validation to ensure no regressions. Since the 4 refinements are already implemented with individual integration tests, I'll create master integration tests that exercise the complete pipeline and validate the interaction between all components.

### Reasoning

I analyzed the current codebase and found that the legacy `TransactionParserService` exists but is not imported or used anywhere in active code. The `MlKitParserService` is now a deprecated façade that properly delegates to `TransactionParsingService`. All 4 refinements have been implemented: strategy pattern (foundation and implementation), configuration centralization, structured logging, and soft-fail state encapsulation. Individual integration tests exist for each refinement, but there's no master test that validates all refinements working together. The codebase is clean with proper dependency injection, immutable models, and extensive test coverage. Performance baselines exist and show good optimization. Test data is comprehensive with English and Spanish samples covering various transaction types, currencies, and edge cases.

## Mermaid Diagram

sequenceDiagram
    participant Test as Master Integration Test
    participant TPS as TransactionParsingService
    participant Config as ParsingConfig
    participant Logger as ParseLogger
    participant LAS as LearnedAssociationStrategy
    participant MKS as MlKitStrategy
    participant FRS as FallbackRegexStrategy
    participant State as PendingTransactionState
    participant Memory as MemoryLogOutput

    Note over Test, Memory: Complete Refinement Integration Flow

    Test->>Config: Create custom ParsingConfig(currency='EUR', threshold=1)
    Test->>Logger: Inject MemoryLogOutput for validation
    Test->>TPS: getInstance(storage, config=customConfig)
    
    TPS->>TPS: Initialize all strategies with custom config
    TPS->>LAS: Inject config, storage, uuid
    TPS->>MKS: Inject config, entityExtractor, services
    TPS->>FRS: Inject config, fallbackParser

    Test->>TPS: parseTransaction("A2B coffee 5 EUR")
    TPS->>Logger: start() → generate correlation ID "abc123"
    TPS->>LAS: execute(context with parseId="abc123")
    LAS->>Logger: d("abc123", "Checking learned associations...")
    LAS->>Config: Use config.defaultCurrency for fallback
    LAS-->>TPS: Return null (no association)

    TPS->>MKS: execute(context with parseId="abc123")
    MKS->>Config: Use config.strictEmbeddedLetterThreshold=1
    MKS->>Logger: d("abc123", "A2B detected as embedded (threshold=1)")
    MKS->>Logger: d("abc123", "Found amount candidates: [5.0]")
    MKS-->>TPS: Return ParseResult.needsCategory()

    TPS->>State: Create PendingTransactionState.forCategorySelection()
    State->>State: Validate parseResult.status == needsCategory
    State-->>Test: Return validated pending state

    Test->>Memory: Capture all log records
    Test->>Test: Validate all messages contain correlation ID "abc123"
    Test->>Config: Verify EUR currency used throughout pipeline
    Test->>State: Verify state properly encapsulates pending data
    Test->>Test: Measure performance < 100ms end-to-end

    Note over Test, Memory: All 4 refinements working together seamlessly

## Proposed File Changes

### lib/services/transaction_parser_service.dart(DELETE)

Remove the legacy TransactionParserService file since it's no longer used anywhere in the codebase.

This file contains the old regex-based parsing logic that has been completely replaced by the new strategy pattern architecture. Analysis confirms:
- No imports of this file exist anywhere in the codebase
- No references to `TransactionParserService` class exist in active code
- The functionality has been superseded by `TransactionParsingService` with the strategy pattern
- All parsing now goes through the new architecture with learned associations, ML Kit, and fallback strategies

Removing this file will:
- Clean up unused legacy code
- Reduce codebase complexity
- Eliminate potential confusion between old and new parsing approaches
- Complete the migration to the new architecture

### test/integration/complete_refinement_integration_test.dart(NEW)

References: 

- test/integration/strategy_pipeline_integration_test.dart
- test/integration/parsing_config_integration_test.dart
- test/integration/pending_state_transitions_test.dart
- test/helpers/test_helpers.dart
- lib/services/parser/parse_logger.dart

Create a comprehensive master integration test that validates all 4 refinements working together in complete end-to-end scenarios.

Implement test scenarios covering:

**Complete Refinement Integration Tests:**
1. **Strategy Pattern + Configuration + Logging + State Management**: Test a complete parsing flow that exercises all strategies with custom configuration, validates structured logging with correlation IDs, and tests soft-fail state transitions

2. **Multi-Stage Soft-Fail with All Refinements**: Test a complex scenario where parsing goes through multiple strategies, requires user input (type → category → amount confirmation), uses custom configuration, and validates that all logging and state management works correctly

3. **Performance with All Refinements**: Validate that the complete refactored architecture maintains or improves performance compared to baseline expectations

4. **Error Handling Across All Refinements**: Test error scenarios that exercise error handling in strategies, configuration validation, logging of errors with correlation IDs, and proper state cleanup

**Specific Integration Scenarios:**
- **Learned Association + Custom Config + Logging**: Learn an association with custom currency config, parse with correlation ID tracking, verify structured logging
- **ML Kit + Configuration + State Transitions**: Parse with ML Kit using custom embedded letter thresholds, trigger amount confirmation, validate state transitions with logging
- **Fallback + All Refinements**: Test fallback parsing with custom config, structured logging, and state management for edge cases
- **Concurrent Operations**: Test multiple parsing operations with different configurations and correlation IDs running concurrently

**Validation Points:**
- All strategies receive and use custom configuration correctly
- Correlation IDs flow through the entire pipeline and appear in all log messages
- State transitions work correctly with the new `PendingTransactionState` model
- Performance is within acceptable bounds (no regressions)
- Memory usage is stable across multiple operations
- Error handling is consistent across all components

**Test Infrastructure:**
- Use all established mocks (`MockStorageService`, `MockEntityExtractor`, `MockCategoryFinderService`, etc.)
- Inject custom `LogOutput` to capture and validate structured logging
- Use `ParsingConfig` with custom values to test configuration propagation
- Create complex parsing scenarios that exercise multiple refinements
- Include performance measurements and memory usage validation
- Test both success and failure paths through the complete pipeline

This master test will serve as the definitive validation that all 4 refinements work together seamlessly and that the refactored architecture maintains all functionality while providing the new capabilities.

### test/integration/parsing_accuracy_validation_test.dart(NEW)

References: 

- test/test_data/sample_transactions.dart
- test/test_data/spanish_sample_transactions.dart
- test/helpers/test_helpers.dart
- lib/services/parser/transaction_parsing_service.dart

Create a comprehensive accuracy validation test to ensure the refactored architecture maintains or improves parsing accuracy compared to the original implementation.

Implement test scenarios covering:

**Parsing Accuracy Validation:**
1. **Regression Testing**: Test a comprehensive set of transaction texts that were known to work with the original architecture and verify they still work correctly

2. **Accuracy Improvements**: Test scenarios where the new strategy pattern should provide better results than the original single-parser approach

3. **Edge Case Handling**: Validate that edge cases are handled as well or better than before

**Test Categories:**
- **Basic Transaction Parsing**: Simple expense, income, and loan transactions with various formats
- **Currency Detection**: Multiple currencies, symbols, codes, and default fallback scenarios
- **Amount Extraction**: Various number formats, abbreviations (k, M, B), embedded numbers, multiple amounts
- **Category Detection**: Keyword-based category detection across different transaction types
- **Type Detection**: Expense/income/loan detection with various linguistic patterns
- **Complex Scenarios**: Multiple numbers, vendor names with embedded numbers, ambiguous amounts
- **Learned Associations**: Verify that learned associations improve accuracy over time
- **Multi-language Support**: Test with different locales and language patterns

**Accuracy Metrics:**
- **Parsing Success Rate**: Percentage of transactions that parse successfully (target ≥97%)
- **Field Accuracy**: Accuracy of amount, type, category, currency detection
- **Ambiguity Handling**: Proper detection and handling of ambiguous scenarios
- **Learning Effectiveness**: Improvement in accuracy after learning associations
- **Performance vs Accuracy Trade-offs**: Ensure performance improvements don't sacrifice accuracy

**Test Data:**
- Use existing sample transaction data from `test_data/sample_transactions.dart` and `test_data/spanish_sample_transactions.dart`
- Include edge cases and regression test scenarios
- Add new test cases that specifically exercise the strategy pattern benefits
- Include real-world transaction examples that were problematic in the original implementation

**Validation Approach:**
- Compare results between strategies to ensure consistency where expected
- Validate that the strategy chain produces optimal results (learned associations take precedence, ML Kit provides better accuracy than regex fallback)
- Test with various configurations to ensure accuracy is maintained across different settings
- Include performance measurements to ensure accuracy improvements don't come at unacceptable performance cost

This test will provide confidence that the refactored architecture maintains the quality of parsing results while providing the architectural benefits of the strategy pattern.

### test/performance/complete_refinement_performance_test.dart(NEW)

References: 

- test/performance/startup_performance_test.dart(MODIFY)
- test/performance/learning_performance_test.dart
- test/helpers/test_helpers.dart
- lib/utils/startup_timer.dart

Create a comprehensive performance regression test to validate that all 4 refinements maintain or improve performance compared to baseline expectations.

Implement performance test scenarios covering:

**Performance Validation Across All Refinements:**
1. **Strategy Pattern Performance**: Measure the overhead of the strategy pattern compared to direct parsing, ensure the chain execution is efficient

2. **Configuration Impact**: Validate that configuration injection and usage doesn't add significant overhead

3. **Logging Performance**: Ensure structured logging with correlation IDs doesn't significantly impact parsing performance

4. **State Management Performance**: Validate that `PendingTransactionState` creation and management is efficient

**Specific Performance Tests:**
- **Single Parse Operation**: Measure end-to-end parsing time for various transaction types and complexities
- **Batch Processing**: Test performance with multiple concurrent parsing operations
- **Strategy Chain Efficiency**: Measure time spent in each strategy and overall chain execution
- **Memory Usage**: Validate memory consumption and ensure no memory leaks across multiple operations
- **Initialization Performance**: Test service initialization time with and without ML Kit
- **Configuration Overhead**: Compare performance with default vs custom configurations
- **Logging Overhead**: Measure performance impact of structured logging vs no logging
- **State Transition Performance**: Measure performance of state creation, transitions, and cleanup

**Performance Benchmarks:**
- **Parsing Speed**: Target <100ms for simple transactions, <500ms for complex scenarios
- **Memory Usage**: Stable memory consumption, no leaks over extended operation
- **Initialization Time**: Service initialization <1000ms, background initialization non-blocking
- **Concurrent Performance**: Linear scaling with number of concurrent operations
- **Strategy Overhead**: Strategy pattern overhead <10% compared to direct parsing

**Test Scenarios:**
- **Simple Transactions**: "coffee $5", "lunch 12.50", "salary $3000"
- **Complex Transactions**: Multiple amounts, embedded numbers, ambiguous scenarios
- **Learned Association Hits**: Transactions that match learned associations (should be fastest)
- **ML Kit Processing**: Transactions that require ML Kit entity extraction
- **Fallback Processing**: Transactions that fall through to regex parsing
- **Multi-stage Soft-fails**: Transactions requiring multiple user interactions

**Performance Monitoring:**
- Use `Stopwatch` for precise timing measurements
- Monitor memory usage with `ProcessInfo` where available
- Track performance across different device capabilities (simulated)
- Include performance regression detection with acceptable thresholds
- Generate performance reports for analysis

**Regression Detection:**
- Compare against baseline performance expectations
- Alert on performance degradation beyond acceptable thresholds
- Track performance trends across test runs
- Validate that new features don't introduce unacceptable overhead

This test will ensure that the architectural improvements don't come at the cost of performance and that the system remains responsive under various load conditions.

### test/performance/startup_performance_test.dart(MODIFY)

References: 

- lib/utils/startup_timer.dart
- lib/services/parser/transaction_parsing_service.dart
- lib/services/parser/mlkit_parser_service.dart

Update the existing startup performance test to use the correct import paths and ensure it works with the final architecture.

Fix the import statements on lines 2-3:
- Change `import 'package:dreamflow/utils/startup_timer.dart';` to `import '../../lib/utils/startup_timer.dart';`
- Change `import 'package:dreamflow/services/parser/transaction_parsing_service.dart';` to `import '../../lib/services/parser/transaction_parsing_service.dart';`

Ensure all tests continue to work with the final refactored architecture:
- Verify that `TransactionParsingService` is properly imported and used
- Ensure all performance expectations are still valid
- Update any test scenarios that might be affected by the final cleanup

Add a test case to validate that the deprecated `MlKitParserService` façade maintains performance compatibility:
- Test that using the deprecated façade doesn't introduce significant performance overhead
- Validate that both `TransactionParsingService` and `MlKitParserService` (façade) have similar performance characteristics
- Ensure backward compatibility doesn't impact performance

This ensures that the startup performance tests continue to work correctly after the final cleanup and provide accurate performance validation for the complete refactored architecture.