import 'package:uuid/uuid.dart';
import '../models/transaction_model.dart';
import '../utils/amount_utils.dart';

class TransactionParserService {
  final Uuid _uuid = Uuid();
  
  /// Parse transaction from text using regular expressions
  /// Returns null if no transaction could be parsed
  Transaction? parseTransaction(String text) {
    // Normalize text for easier parsing
    final normalizedText = text.toLowerCase().trim();
    
    // Try to detect transaction type
    TransactionType? detectedType = _detectTransactionType(normalizedText);
    if (detectedType == null) return null;
    
    // Try to extract amount
    double? amount = _extractAmount(normalizedText);
    if (amount == null) return null;
    
    // Try to detect category
    String categoryId = _detectCategory(normalizedText, detectedType);
    
    // Create description from the text
    String description = _createDescription(normalizedText);
    
    // Extract tags if any
    List<String> tags = _extractTags(normalizedText);
    
    // Create and return the transaction
    return Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: detectedType,
      categoryId: categoryId,
      date: DateTime.now(),
      description: description,
      tags: tags,
    );
  }
  
  /// Detect transaction type from text
  TransactionType? _detectTransactionType(String text) {
    // Check for negative sign or minus at the beginning which indicates expense
    if (RegExp(r'^\s*-').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Expense patterns
    if (RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought|bought for)')
        .hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Income patterns
    if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)')
        .hasMatch(text)) {
      return TransactionType.income;
    }
    
    // Loan patterns
    if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)')
        .hasMatch(text)) {
      return TransactionType.loan;
    }
    
    // Special case: 'for' keyword typically indicates expense unless preceded by income keyword
    if (RegExp(r'\bfor\b').hasMatch(text) && 
        !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    // Default to expense if contains $ or other currency symbols
    if (RegExp(r'[$€£¥]').hasMatch(text)) {
      return TransactionType.expense;
    }
    
    return null;
  }
  
  /// Extract amount from text
  double? _extractAmount(String text) {
    // Handle negative amount pattern first (like -500$ for toys)
    if (text.startsWith('-')) {
      // Remove the leading minus for easier parsing
      final trimmedText = text.substring(1).trim();
      final amount = _extractPositiveAmount(trimmedText);
      return amount; // It's already an expense based on detection type
    }
    
    // Regular amount extraction for positive values
    return _extractPositiveAmount(text);
  }
  
  /// Helper to extract positive amount values from text with abbreviation support
  double? _extractPositiveAmount(String text) {
    // Use AmountUtils for enhanced amount extraction with abbreviation support
    final result = AmountUtils.extractAmountFromText(text);

    if (result != null) {
      return result['amount'] as double;
    }

    // Fallback to original regex for backward compatibility
    final amountRegex = RegExp(r'\$?\s?(\d+[.,]?\d*)\s?(?:dollars|USD|\$)?');
    final match = amountRegex.firstMatch(text);

    if (match != null) {
      final amountStr = match.group(1)?.replaceAll(',', '.') ?? '';
      try {
        return double.parse(amountStr);
      } catch (e) {
        return null;
      }
    }

    return null;
  }
  
  /// Detect category based on keywords in text
  String _detectCategory(String text, TransactionType type) {
    // Expense categories
    if (type == TransactionType.expense) {
      if (_containsAny(text, ['food', 'meal', 'restaurant', 'lunch', 'dinner', 'breakfast', 'grocery', 'pizza', 'burger', 'cafe', 'coffee', 'snack'])) {
        return 'food';
      }
      if (_containsAny(text, ['transport', 'gas', 'fuel', 'uber', 'taxi', 'car', 'bus', 'train', 'transportation', 'fare', 'metro', 'subway', 'parking'])) {
        return 'transport';
      }
      if (_containsAny(text, ['shopping', 'clothes', 'shoes', 'dress', 'shirt', 'amazon', 'online', 'buy', 'purchase', 'mall', 'store', 'retail', 'toys', 'toy'])) {
        return 'shopping';
      }
      if (_containsAny(text, ['utilities', 'bill', 'electricity', 'water', 'gas bill', 'internet', 'phone', 'wifi', 'broadband', 'service', 'subscription'])) {
        return 'utilities';
      }
      if (_containsAny(text, ['entertainment', 'movie', 'cinema', 'concert', 'show', 'theater', 'game', 'subscription', 'netflix', 'spotify', 'music', 'streaming', 'app'])) {
        return 'entertainment';
      }
      if (_containsAny(text, ['health', 'doctor', 'medicine', 'hospital', 'medical', 'pharmacy', 'dentist', 'clinic', 'therapy', 'treatment', 'drug'])) {
        return 'health';
      }
      if (_containsAny(text, ['gift', 'present', 'donation', 'charity', 'tip', 'birthday', 'anniversary'])) {
        return 'gift';
      }
    }
    
    // Income categories
    if (type == TransactionType.income) {
      if (_containsAny(text, ['salary', 'wage', 'paycheck', 'payment', 'work', 'job', 'employment', 'employer', 'pay', 'compensation'])) {
        return 'salary';
      }
      if (_containsAny(text, ['gift', 'present', 'donation', 'birthday', 'holiday', 'christmas', 'received money'])) {
        return 'gift';
      }
      if (_containsAny(text, ['interest', 'dividend', 'investment', 'stock', 'return', 'capital gain', 'yield', 'shares', 'mutual fund', 'etf'])) {
        return 'investment';
      }
      if (_containsAny(text, ['bonus', 'commission', 'incentive', 'reward', 'performance', 'achievement'])) {
        return 'bonus';
      }
      if (_containsAny(text, ['selling', 'sold', 'sale', 'resale', 'marketplace', 'ebay', 'craigslist', 'facebook marketplace', 'garage sale'])) {
        return 'sales';
      }
      if (_containsAny(text, ['crypto', 'bitcoin', 'ethereum', 'cryptocurrency', 'token', 'blockchain', 'mining'])) {
        return 'crypto';
      }
      if (_containsAny(text, ['refund', 'reimbursement', 'cashback', 'return'])) {
        return 'refund';
      }
    }
    
    // Loan category
    if (type == TransactionType.loan) {
      return 'loan';
    }
    
    // Default categories based on transaction type
    switch (type) {
      case TransactionType.expense:
        return 'shopping';
      case TransactionType.income:
        return 'other_income';
      case TransactionType.loan:
        return 'loan';
    }
  }
  
  /// Check if text contains any of the words
  bool _containsAny(String text, List<String> words) {
    for (final word in words) {
      if (text.contains(word)) {
        return true;
      }
    }
    return false;
  }
  
  /// Create a meaningful description from the text
  String _createDescription(String text) {
    // Remove currency symbols and amounts
    final cleanedText = text
        .replaceAll(RegExp(r'\$?\s?\d+[.,]?\d*\s?(?:dollars|USD|\$)?'), '')
        .replaceAll(RegExp(r'spent|paid|bought|received|earned|borrowed|lent'), '')
        .trim();
    
    // If text is too short, return original text
    if (cleanedText.length < 5 && text.length > 5) {
      return text;
    }
    
    // Capitalize first letter
    if (cleanedText.isNotEmpty) {
      return cleanedText[0].toUpperCase() + cleanedText.substring(1);
    }
    
    return 'Transaction';
  }
  
  /// Extract tags from text (words with # prefix)
  List<String> _extractTags(String text) {
    final tagRegex = RegExp(r'#(\w+)');
    final matches = tagRegex.allMatches(text);
    
    if (matches.isNotEmpty) {
      return matches.map((match) => match.group(1) ?? '').where((tag) => tag.isNotEmpty).toList();
    }
    
    return [];
  }
}