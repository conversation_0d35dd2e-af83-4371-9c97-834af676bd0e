import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:money_lover_chat/widgets/quick_reply_widget.dart';

void main() {
  group('QuickReplyWidget Tests', () {
    testWidgets('should create basic widget', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: ['Test'],
              onReplySelected: (option) {},
            ),
          ),
        ),
      );

      expect(find.byType(QuickReplyWidget), findsOneWidget);
      expect(find.text('Test'), findsOneWidget);
    });

    testWidgets('should handle user interaction', (WidgetTester tester) async {
      String? selectedOption;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: ['Option 1', 'Option 2'],
              onReplySelected: (option) {
                selectedOption = option;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(selectedOption, equals('Option 1'));
    });

    testWidgets('should handle empty options', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: [],
              onReplySelected: (option) {},
            ),
          ),
        ),
      );

      expect(find.byType(QuickReplyWidget), findsOneWidget);
      expect(find.byType(InkWell), findsNothing);
    });

    testWidgets('should handle disabled state', (WidgetTester tester) async {
      bool callbackCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: ['Test'],
              onReplySelected: (option) {
                callbackCalled = true;
              },
              enabled: false,
            ),
          ),
        ),
      );

      await tester.tap(find.text('Test'));
      await tester.pump();

      expect(callbackCalled, isFalse);
    });
  });

  group('Widget Layout Tests', () {
    testWidgets('should use Wrap layout for multiple options', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: ['Option 1', 'Option 2', 'Option 3'],
              onReplySelected: (option) {},
            ),
          ),
        ),
      );

      expect(find.byType(Wrap), findsOneWidget);
    });

    testWidgets('should handle long text gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: ['This is a very long option text that might wrap'],
              onReplySelected: (option) {},
            ),
          ),
        ),
      );

      expect(find.text('This is a very long option text that might wrap'), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('should have proper accessibility', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: ['Accessible Option'],
              onReplySelected: (option) {},
            ),
          ),
        ),
      );

      final text = find.text('Accessible Option');
      expect(text, findsOneWidget);

      final textWidget = tester.widget<Text>(text);
      expect(textWidget.semanticsLabel, contains('Quick reply'));
    });
  });
}
