import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/services/parser/entity_extractor_base.dart';
import '../../lib/models/transaction_model.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_entity_extractor.dart';
import '../test_data/sample_transactions.dart';
import '../test_data/spanish_sample_transactions.dart';

/// Comprehensive accuracy validation test to ensure the refactored architecture
/// maintains or improves parsing accuracy compared to the original implementation.
/// 
/// Tests:
/// 1. Regression Testing - Known working transaction texts
/// 2. Accuracy Improvements - Strategy pattern benefits
/// 3. Edge Case Handling - Complex scenarios
/// 4. Multi-language Support - Spanish localization
/// 5. Learning Effectiveness - Accuracy improvement over time
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Parsing Accuracy Validation Tests', () {
    late MockStorageService mockStorage;
    late MockEntityExtractor mockExtractor;

    setUp(() async {
      TransactionParsingService.resetInstance();
      mockStorage = MockStorageService();
      mockExtractor = MockEntityExtractor();
      
      await mockStorage.init();
      mockStorage.setString('default_currency', 'USD');
      mockStorage.setString('categories', '[]');
    });

    tearDown(() {
      TransactionParsingService.resetInstance();
    });

    group('Regression Testing - Known Working Transactions', () {
      test('Simple Expenses - Should maintain 100% accuracy', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        int successCount = 0;
        int totalCount = 0;

        for (final entry in SampleTransactions.simpleExpenses.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          // Configure mock extractor for this specific test
          _configureMockExtractorForText(mockExtractor, text, expectedAmount, expectedCurrency);

          final result = await service.parseTransaction(text);
          totalCount++;

          if (result.isSuccess || result.requiresUserInput) {
            successCount++;
            
            // Validate core parsing accuracy
            expect(result.transaction.amount, equals(expectedAmount), 
                reason: 'Amount mismatch for: $text');
            expect(result.transaction.currencyCode, equals(expectedCurrency),
                reason: 'Currency mismatch for: $text');
            expect(result.transaction.type, equals(expectedType),
                reason: 'Type mismatch for: $text');
          }
        }

        // Target: 100% success rate for simple expenses
        final successRate = (successCount / totalCount) * 100;
        expect(successRate, equals(100.0), 
            reason: 'Simple expenses should have 100% success rate, got $successRate%');
      });

      test('Income Transactions - Should maintain high accuracy', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        int successCount = 0;
        int totalCount = 0;

        for (final entry in SampleTransactions.incomeTransactions.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          _configureMockExtractorForText(mockExtractor, text, expectedAmount, expectedCurrency);

          final result = await service.parseTransaction(text);
          totalCount++;

          if (result.isSuccess || result.requiresUserInput) {
            successCount++;
            
            expect(result.transaction.amount, equals(expectedAmount));
            expect(result.transaction.currencyCode, equals(expectedCurrency));
            expect(result.transaction.type, equals(expectedType));
          }
        }

        // Target: ≥95% success rate for income transactions
        final successRate = (successCount / totalCount) * 100;
        expect(successRate, greaterThanOrEqualTo(95.0),
            reason: 'Income transactions should have ≥95% success rate, got $successRate%');
      });

      test('Complex Scenarios - Should handle edge cases gracefully', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        int successCount = 0;
        int totalCount = 0;

        for (final entry in SampleTransactions.complexTransactions.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double?;
          final expectedCurrency = testData['expected_currency'] as String?;

          if (expectedAmount != null && expectedCurrency != null) {
            _configureMockExtractorForText(mockExtractor, text, expectedAmount, expectedCurrency);
          } else {
            // Configure for ambiguous/complex scenarios
            mockExtractor.setMockResults([
              MockEntityAnnotation(text: 'complex', start: 0, end: 7, entityType: EntityType.other),
              MockEntityAnnotation(text: 'scenario', start: 8, end: 16, entityType: EntityType.other),
            ]);
          }

          final result = await service.parseTransaction(text);
          totalCount++;

          // For complex scenarios, we accept both success and user input required
          if (result.isSuccess || result.requiresUserInput) {
            successCount++;
          }
        }

        // Target: ≥85% success rate for complex scenarios (including soft-fails)
        final successRate = (successCount / totalCount) * 100;
        expect(successRate, greaterThanOrEqualTo(85.0),
            reason: 'Complex scenarios should have ≥85% success rate, got $successRate%');
      });
    });

    group('Multi-language Support - Spanish Localization', () {
      test('Spanish Simple Expenses - Should maintain accuracy', () async {
        // Configure for Spanish locale
        await mockStorage.saveDefaultCurrency('EUR');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        int successCount = 0;
        int totalCount = 0;

        for (final entry in SpanishSampleTransactions.simpleExpenses.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          _configureMockExtractorForText(mockExtractor, text, expectedAmount, expectedCurrency);

          final result = await service.parseTransaction(text);
          totalCount++;

          if (result.isSuccess || result.requiresUserInput) {
            successCount++;
            
            expect(result.transaction.amount, equals(expectedAmount));
            expect(result.transaction.currencyCode, equals(expectedCurrency));
            expect(result.transaction.type, equals(expectedType));
          }
        }

        // Target: ≥90% success rate for Spanish transactions
        final successRate = (successCount / totalCount) * 100;
        expect(successRate, greaterThanOrEqualTo(90.0),
            reason: 'Spanish transactions should have ≥90% success rate, got $successRate%');
      });
    });

    group('Learning Effectiveness - Accuracy Improvement', () {
      test('Should improve accuracy after learning associations', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Test text that initially might be ambiguous
        const testText = 'coffee 5';
        
        // Configure mock for initial parsing
        mockExtractor.setMockResults([
          MockEntityAnnotation(text: 'coffee', start: 0, end: 6, entityType: EntityType.other),
          MockEntityAnnotation(text: '5', start: 7, end: 8, entityType: EntityType.money),
        ]);

        // First parse - might require user input
        await service.parseTransaction(testText);

        // Learn the association
        await service.learnCategory(testText, 'food');

        // Second parse - should be more accurate/faster
        final improvedResult = await service.parseTransaction(testText);
        
        // After learning, should get direct success
        expect(improvedResult.isSuccess, isTrue);
        expect(improvedResult.transaction.amount, equals(5.0));
        expect(improvedResult.transaction.categoryId, equals('food'));
        expect(improvedResult.transaction.type, equals(TransactionType.expense));
      });

      test('Should maintain learned associations across multiple parses', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Learn multiple associations
        final associations = [
          {'text': 'starbucks 6', 'category': 'food', 'amount': 6.0},
          {'text': 'uber 15', 'category': 'transport', 'amount': 15.0},
          {'text': 'netflix 12', 'category': 'entertainment', 'amount': 12.0},
        ];

        for (final assoc in associations) {
          await service.learnCategory(assoc['text'] as String, assoc['category'] as String);
        }

        // Test all learned associations
        int directSuccessCount = 0;
        for (final assoc in associations) {
          final result = await service.parseTransaction(assoc['text'] as String);
          
          if (result.isSuccess) {
            directSuccessCount++;
            expect(result.transaction.amount, equals(assoc['amount']));
            expect(result.transaction.categoryId, equals(assoc['category']));
          }
        }

        // All learned associations should result in direct success
        expect(directSuccessCount, equals(associations.length));
      });
    });

    group('Performance vs Accuracy Trade-offs', () {
      test('Should maintain accuracy while improving performance', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Test a variety of transaction types
        final testCases = [
          ...SampleTransactions.simpleExpenses.values,
          ...SampleTransactions.incomeTransactions.values,
        ];

        int successCount = 0;
        final stopwatch = Stopwatch()..start();

        for (final testData in testCases) {
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;

          _configureMockExtractorForText(mockExtractor, text, expectedAmount, expectedCurrency);

          final operationStart = stopwatch.elapsedMilliseconds;
          final result = await service.parseTransaction(text);
          final operationTime = stopwatch.elapsedMilliseconds - operationStart;

          // Performance check: each operation should be fast
          expect(operationTime, lessThan(100), 
              reason: 'Operation took too long: ${operationTime}ms for "$text"');

          if (result.isSuccess || result.requiresUserInput) {
            successCount++;
          }
        }

        stopwatch.stop();

        // Accuracy check: should maintain high success rate
        final successRate = (successCount / testCases.length) * 100;
        expect(successRate, greaterThanOrEqualTo(95.0));

        // Overall performance check
        final avgTimePerOperation = stopwatch.elapsedMilliseconds / testCases.length;
        expect(avgTimePerOperation, lessThan(50), 
            reason: 'Average operation time too high: ${avgTimePerOperation}ms');
      });
    });
  });
}

/// Helper function to configure mock extractor for specific text parsing
void _configureMockExtractorForText(MockEntityExtractor extractor, String text, double amount, String currency) {
  final entities = <MockEntityAnnotation>[];

  // Add amount entity
  final amountStr = amount.toString();
  final amountIndex = text.indexOf(amountStr);
  if (amountIndex >= 0) {
    entities.add(MockEntityAnnotation(
      text: amountStr,
      start: amountIndex,
      end: amountIndex + amountStr.length,
      entityType: EntityType.money,
    ));
  }

  // Add currency entity if present in text
  final currencySymbol = _getCurrencySymbol(currency);
  final currencyIndex = text.indexOf(currencySymbol);
  if (currencyIndex >= 0) {
    entities.add(MockEntityAnnotation(
      text: currencySymbol,
      start: currencyIndex,
      end: currencyIndex + currencySymbol.length,
      entityType: EntityType.other,
    ));
  }

  extractor.setMockResults(entities);
}

/// Helper function to get currency symbol for a currency code
String _getCurrencySymbol(String currencyCode) {
  switch (currencyCode) {
    case 'USD': return '\$';
    case 'EUR': return '€';
    case 'GBP': return '£';
    case 'JPY': return '¥';
    default: return currencyCode;
  }
}
