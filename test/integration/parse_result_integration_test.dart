import 'package:flutter_test/flutter_test.dart';
import 'package:money_lover_chat/models/parse_result.dart';
import 'package:money_lover_chat/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('ParseResult Integration Tests', () {
    group('Soft Fail Flow Integration', () {
      test('should handle complete type disambiguation flow', () {
        // Step 1: Create partial transaction (amount extracted, type unclear)
        final partialTransaction = TestHelpers.createTestTransaction(
          amount: 200000,
          currency: 'VND',
          description: 'to travel',
        );

        // Step 2: Parser returns needsType result
        final parseResult = ParseResult.needsType(partialTransaction);
        
        // Step 3: Verify soft fail state
        expect(parseResult.status, equals(ParseStatus.needsType));
        expect(parseResult.needsTypeSelection, isTrue);
        expect(parseResult.needsCategorySelection, isFalse);
        expect(parseResult.isSuccess, isFalse);
        expect(parseResult.requiresUserInput, isTrue);
        expect(parseResult.error, isNull);

        // Step 4: Verify partial transaction data is preserved
        expect(parseResult.transaction.amount, equals(200000));
        expect(parseResult.transaction.currency, equals('VND'));
        expect(parseResult.transaction.description, equals('to travel'));

        // Step 5: Simulate user selecting "Expense"
        final completedTransaction = parseResult.transaction.copyWith(
          type: TransactionType.expense,
        );

        // Step 6: Create successful result
        final successResult = ParseResult.success(completedTransaction);
        
        // Step 7: Verify completion
        expect(successResult.status, equals(ParseStatus.success));
        expect(successResult.isSuccess, isTrue);
        expect(successResult.requiresUserInput, isFalse);
        expect(successResult.transaction.type, equals(TransactionType.expense));
        expect(successResult.transaction.amount, equals(200000));
      });

      test('should handle complete category disambiguation flow', () {
        // Step 1: Create transaction with type but unclear category
        final partialTransaction = TestHelpers.createTestTransaction(
          amount: 50000,
          currency: 'VND',
          type: TransactionType.expense,
          description: 'lunch',
        );

        // Step 2: Parser returns needsCategory result
        final parseResult = ParseResult.needsCategory(partialTransaction);
        
        // Step 3: Verify soft fail state
        expect(parseResult.status, equals(ParseStatus.needsCategory));
        expect(parseResult.needsCategorySelection, isTrue);
        expect(parseResult.needsTypeSelection, isFalse);
        expect(parseResult.isSuccess, isFalse);
        expect(parseResult.requiresUserInput, isTrue);

        // Step 4: Verify transaction data is preserved
        expect(parseResult.transaction.type, equals(TransactionType.expense));
        expect(parseResult.transaction.description, equals('lunch'));

        // Step 5: Simulate user selecting "Food" category
        final foodCategory = TestHelpers.createTestCategory(name: 'Food');
        final completedTransaction = parseResult.transaction.copyWith(
          category: foodCategory,
        );

        // Step 6: Create successful result
        final successResult = ParseResult.success(completedTransaction);
        
        // Step 7: Verify completion
        expect(successResult.isSuccess, isTrue);
        expect(successResult.transaction.category?.name, equals('Food'));
      });

      test('should handle failed parsing gracefully', () {
        // Step 1: Parser fails to extract any meaningful data
        final failedResult = ParseResult.failed('Unable to extract transaction details from input');
        
        // Step 2: Verify failed state
        expect(failedResult.status, equals(ParseStatus.failed));
        expect(failedResult.isSuccess, isFalse);
        expect(failedResult.requiresUserInput, isFalse);
        expect(failedResult.needsTypeSelection, isFalse);
        expect(failedResult.needsCategorySelection, isFalse);
        expect(failedResult.error, equals('Unable to extract transaction details from input'));
        expect(failedResult.transaction, isNull);

        // Step 3: User can retry with different input
        // This would be handled by the UI layer
        final retryTransaction = TestHelpers.createTestTransaction(
          amount: 100000,
          currency: 'VND',
          type: TransactionType.expense,
        );
        
        final retryResult = ParseResult.success(retryTransaction);
        expect(retryResult.isSuccess, isTrue);
      });

      test('should handle multiple disambiguation steps', () {
        // Step 1: Start with minimal transaction data
        final initialTransaction = TestHelpers.createTestTransaction(
          amount: 75000,
          currency: 'VND',
          description: 'shopping',
        );

        // Step 2: First disambiguation - needs type
        final typeResult = ParseResult.needsType(initialTransaction);
        expect(typeResult.needsTypeSelection, isTrue);

        // Step 3: User selects type
        final withTypeTransaction = typeResult.transaction.copyWith(
          type: TransactionType.expense,
        );

        // Step 4: Second disambiguation - needs category
        final categoryResult = ParseResult.needsCategory(withTypeTransaction);
        expect(categoryResult.needsCategorySelection, isTrue);
        expect(categoryResult.transaction.type, equals(TransactionType.expense));

        // Step 5: User selects category
        final shoppingCategory = TestHelpers.createTestCategory(name: 'Shopping');
        final finalTransaction = categoryResult.transaction.copyWith(
          category: shoppingCategory,
        );

        // Step 6: Final success
        final successResult = ParseResult.success(finalTransaction);
        expect(successResult.isSuccess, isTrue);
        expect(successResult.transaction.type, equals(TransactionType.expense));
        expect(successResult.transaction.category?.name, equals('Shopping'));
      });

      test('should preserve all transaction data through disambiguation', () {
        // Step 1: Create rich partial transaction
        final richTransaction = TestHelpers.createTestTransaction(
          amount: 1500000,
          currency: 'VND',
          description: 'monthly rent payment',
          tags: ['monthly', 'housing', 'recurring'],
        );

        // Step 2: Needs type disambiguation
        final typeResult = ParseResult.needsType(richTransaction);
        
        // Step 3: Verify all data is preserved
        expect(typeResult.transaction.amount, equals(1500000));
        expect(typeResult.transaction.currency, equals('VND'));
        expect(typeResult.transaction.description, equals('monthly rent payment'));
        expect(typeResult.transaction.tags, equals(['monthly', 'housing', 'recurring']));

        // Step 4: Complete with type
        final completedTransaction = typeResult.transaction.copyWith(
          type: TransactionType.expense,
        );

        final successResult = ParseResult.success(completedTransaction);
        
        // Step 5: Verify all original data is still present
        expect(successResult.transaction.amount, equals(1500000));
        expect(successResult.transaction.currency, equals('VND'));
        expect(successResult.transaction.description, equals('monthly rent payment'));
        expect(successResult.transaction.tags, equals(['monthly', 'housing', 'recurring']));
        expect(successResult.transaction.type, equals(TransactionType.expense));
      });
    });

    group('Edge Cases and Error Recovery', () {
      test('should handle zero amount transactions', () {
        final zeroTransaction = TestHelpers.createTestTransaction(
          amount: 0,
          currency: 'VND',
          description: 'test transaction',
        );

        final result = ParseResult.needsType(zeroTransaction);
        expect(result.transaction.amount, equals(0));
        expect(result.needsTypeSelection, isTrue);
      });

      test('should handle very large amounts', () {
        final largeTransaction = TestHelpers.createTestTransaction(
          amount: 999999999999,
          currency: 'VND',
          description: 'large transaction',
        );

        final result = ParseResult.needsCategory(largeTransaction);
        expect(result.transaction.amount, equals(999999999999));
        expect(result.needsCategorySelection, isTrue);
      });

      test('should handle special characters in descriptions', () {
        final specialTransaction = TestHelpers.createTestTransaction(
          description: 'café & restaurant - 50% discount! @downtown',
        );

        final result = ParseResult.needsType(specialTransaction);
        expect(result.transaction.description, equals('café & restaurant - 50% discount! @downtown'));
      });

      test('should handle empty descriptions', () {
        final emptyDescTransaction = TestHelpers.createTestTransaction(
          description: '',
        );

        final result = ParseResult.needsCategory(emptyDescTransaction);
        expect(result.transaction.description, equals(''));
        expect(result.needsCategorySelection, isTrue);
      });

      test('should handle different currency formats', () {
        final currencies = ['VND', 'USD', 'EUR', 'JPY', 'GBP'];
        
        for (final currency in currencies) {
          final transaction = TestHelpers.createTestTransaction(
            currency: currency,
            amount: 100,
          );

          final result = ParseResult.needsType(transaction);
          expect(result.transaction.currency, equals(currency));
          expect(result.needsTypeSelection, isTrue);
        }
      });

      test('should handle rapid state transitions', () {
        final transaction = TestHelpers.createTestTransaction();

        // Rapid transitions through different states
        final needsTypeResult = ParseResult.needsType(transaction);
        expect(needsTypeResult.status, equals(ParseStatus.needsType));

        final withType = needsTypeResult.transaction.copyWith(type: TransactionType.expense);
        final needsCategoryResult = ParseResult.needsCategory(withType);
        expect(needsCategoryResult.status, equals(ParseStatus.needsCategory));

        final withCategory = needsCategoryResult.transaction.copyWith(
          category: TestHelpers.createTestCategory(),
        );
        final successResult = ParseResult.success(withCategory);
        expect(successResult.status, equals(ParseStatus.success));

        // All transitions should maintain data integrity
        expect(successResult.transaction.id, equals(transaction.id));
        expect(successResult.transaction.amount, equals(transaction.amount));
      });

      test('should handle error messages with special characters', () {
        final errorMessages = [
          'Error: Unable to parse "café & restaurant"',
          'Network timeout: 30s exceeded',
          'Invalid format: expected "amount currency" but got "xyz"',
          'Service unavailable (503)',
        ];

        for (final errorMessage in errorMessages) {
          final result = ParseResult.failed(errorMessage);
          expect(result.status, equals(ParseStatus.failed));
          expect(result.error, equals(errorMessage));
          expect(result.isSuccess, isFalse);
        }
      });

      test('should validate enum consistency', () {
        // Test all ParseStatus enum values
        final statuses = [
          ParseStatus.success,
          ParseStatus.needsCategory,
          ParseStatus.needsType,
          ParseStatus.failed,
        ];

        for (final status in statuses) {
          expect(status, isNotNull);
          expect(status.toString(), contains('ParseStatus.'));
        }

        // Test enum comparisons
        expect(ParseStatus.success, isNot(equals(ParseStatus.failed)));
        expect(ParseStatus.needsType, isNot(equals(ParseStatus.needsCategory)));
      });

      test('should handle concurrent parse operations', () {
        // Simulate multiple parse operations happening concurrently
        final transactions = List.generate(10, (index) => 
          TestHelpers.createTestTransaction(
            amount: (index + 1) * 10000,
            description: 'Transaction $index',
          )
        );

        final results = transactions.map((transaction) {
          // Alternate between different result types
          if (transaction.amount! % 30000 == 0) {
            return ParseResult.failed('Test error');
          } else if (transaction.amount! % 20000 == 0) {
            return ParseResult.needsCategory(transaction);
          } else if (transaction.amount! % 10000 == 0) {
            return ParseResult.needsType(transaction);
          } else {
            return ParseResult.success(transaction);
          }
        }).toList();

        // Verify all results are valid
        expect(results.length, equals(10));
        
        for (int i = 0; i < results.length; i++) {
          final result = results[i];
          expect(result, isNotNull);
          expect(result.status, isNotNull);
          
          if (result.status != ParseStatus.failed) {
            expect(result.transaction, isNotNull);
            expect(result.transaction!.amount, equals((i + 1) * 10000));
          }
        }
      });
    });

    group('Performance and Scalability', () {
      test('should handle large transaction objects efficiently', () {
        // Create transaction with large amounts of data
        final largeTransaction = TestHelpers.createTestTransaction(
          description: 'A' * 1000, // Very long description
          tags: List.generate(100, (index) => 'tag$index'), // Many tags
        );

        final startTime = DateTime.now();
        
        // Perform multiple operations
        final needsTypeResult = ParseResult.needsType(largeTransaction);
        final withType = needsTypeResult.transaction.copyWith(type: TransactionType.expense);
        final needsCategoryResult = ParseResult.needsCategory(withType);
        final withCategory = needsCategoryResult.transaction.copyWith(
          category: TestHelpers.createTestCategory(),
        );
        final successResult = ParseResult.success(withCategory);
        
        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);
        
        // Should complete quickly (under 100ms for this simple test)
        expect(duration.inMilliseconds, lessThan(100));
        
        // Verify data integrity
        expect(successResult.transaction.description, equals('A' * 1000));
        expect(successResult.transaction.tags?.length, equals(100));
      });

      test('should handle many rapid parse result creations', () {
        final startTime = DateTime.now();
        
        // Create many parse results rapidly
        final results = <ParseResult>[];
        for (int i = 0; i < 1000; i++) {
          final transaction = TestHelpers.createTestTransaction(amount: i);
          results.add(ParseResult.needsType(transaction));
        }
        
        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);
        
        // Should complete quickly
        expect(duration.inMilliseconds, lessThan(1000));
        expect(results.length, equals(1000));
        
        // Verify all results are valid
        for (int i = 0; i < results.length; i++) {
          expect(results[i].status, equals(ParseStatus.needsType));
          expect(results[i].transaction.amount, equals(i));
        }
      });
    });
  });
}
