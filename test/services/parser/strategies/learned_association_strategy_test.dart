import 'package:flutter_test/flutter_test.dart';
import 'package:uuid/uuid.dart';
import '../../../../lib/services/parser/strategies/learned_association_strategy.dart';
import '../../../../lib/services/parser/parsing_context.dart';
import '../../../../lib/services/parser/learned_association_service.dart';
import '../../../../lib/models/transaction_model.dart';
import '../../../helpers/test_helpers.dart';
import '../../../mocks/mock_storage_service.dart';

void main() {
  group('LearnedAssociationStrategy Tests', () {
    late LearnedAssociationStrategy strategy;
    late LearnedAssociationService learnedService;
    late MockStorageService mockStorage;
    late Uuid uuid;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      uuid = const Uuid();

      // Use real service with mock storage
      learnedService = await LearnedAssociationService.getInstance(mockStorage);

      strategy = LearnedAssociationStrategy(
        learnedService,
        mockStorage,
        uuid,
      );
    });

    tearDown(() {
      LearnedAssociationService.resetInstance();
    });

    group('Strategy Interface Compliance', () {
      test('should return correct strategy name', () {
        expect(strategy.name, equals('LearnedAssociationStrategy'));
      });

      test('should return ParseResult when association found', () async {
        // Setup learned association using the real service
        await learnedService.learn('coffee at starbucks',
          type: TransactionType.expense,
          categoryId: 'food',
          confirmedAmount: 25.50,
        );

        final context = ParsingContext(text: 'coffee at starbucks');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
        expect(result.transaction.categoryId, equals('food'));
      });

      test('should return null when no association found', () async {
        final context = ParsingContext(text: 'unknown transaction');
        final result = await strategy.execute(context);

        expect(result, isNull);
      });
    });

    group('Association Found Cases', () {
      test('should build transaction from association with confirmed amount', () async {
        await learnedService.learn('monthly salary',
          type: TransactionType.income,
          categoryId: 'salary',
          confirmedAmount: 5000.0,
        );

        final context = ParsingContext(text: 'monthly salary');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(ParseResultMatchers.isSuccess(result!), isTrue);
        expect(result.transaction.amount, equals(5000.0));
        expect(result.transaction.type, equals(TransactionType.income));
        expect(result.transaction.categoryId, equals('salary'));
        expect(result.transaction.description, equals('monthly salary'));
      });

      test('should extract amount from text when no confirmed amount in association', () async {
        await learnedService.learn('starbucks',
          type: TransactionType.expense,
          categoryId: 'food',
          // No confirmed amount
        );

        final context = ParsingContext(text: 'starbucks coffee \$12.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(ParseResultMatchers.isSuccess(result!), isTrue);
        expect(result.transaction.amount, equals(12.50));
        expect(result.transaction.type, equals(TransactionType.expense));
        expect(result.transaction.categoryId, equals('food'));
      });

      test('should handle association with all fields populated', () async {
        await learnedService.learn('loan payment',
          type: TransactionType.loan,
          categoryId: 'debt',
          confirmedAmount: 1000.0,
        );

        final context = ParsingContext(text: 'loan payment #monthly');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(ParseResultMatchers.isSuccess(result!), isTrue);
        expect(result.transaction.amount, equals(1000.0));
        expect(result.transaction.type, equals(TransactionType.loan));
        expect(result.transaction.categoryId, equals('debt'));
        expect(result.transaction.tags, contains('monthly'));
      });

      test('should use default values when association has null fields', () async {
        // Learn with minimal data (only categoryId)
        await learnedService.learn('unknown transaction', categoryId: 'other');

        final context = ParsingContext(text: 'unknown transaction \$50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(ParseResultMatchers.isSuccess(result!), isTrue);
        expect(result.transaction.amount, equals(50.0));
        expect(result.transaction.type, equals(TransactionType.expense)); // Default
        expect(result.transaction.categoryId, equals('other'));
      });
    });

    group('No Association Cases', () {
      test('should return null for completely unknown text', () async {
        final context = ParsingContext(text: 'completely unknown transaction');
        final result = await strategy.execute(context);

        expect(result, isNull);
      });

      test('should return null for empty text', () async {
        final context = ParsingContext(text: '');
        final result = await strategy.execute(context);

        expect(result, isNull);
      });

      test('should return null for whitespace-only text', () async {
        final context = ParsingContext(text: '   \n\t  ');
        final result = await strategy.execute(context);

        expect(result, isNull);
      });
    });

    group('Currency Handling', () {
      test('should extract currency from text when present', () async {
        await learnedService.learn('lunch',
          type: TransactionType.expense,
          categoryId: 'food',
          confirmedAmount: 25.0,
        );

        final context = ParsingContext(text: 'lunch €25');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('EUR'));
      });

      test('should use default currency when no currency in text', () async {
        mockStorage.setString('default_currency', 'GBP');

        await learnedService.learn('taxi',
          type: TransactionType.expense,
          categoryId: 'transport',
          confirmedAmount: 15.0,
        );

        final context = ParsingContext(text: 'taxi ride');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('GBP'));
      });

      test('should handle currency codes in text', () async {
        await learnedService.learn('amazon',
          type: TransactionType.expense,
          categoryId: 'shopping',
          confirmedAmount: 100.0,
        );

        final context = ParsingContext(text: 'amazon purchase 100 JPY');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('JPY'));
      });
    });

    group('Amount Extraction', () {
      test('should prioritize confirmed amount over text amount', () async {
        await learnedService.learn('pizza',
          type: TransactionType.expense,
          categoryId: 'food',
          confirmedAmount: 30.0, // Confirmed amount
        );

        final context = ParsingContext(text: 'pizza delivery \$25'); // Text has different amount
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(30.0)); // Should use confirmed amount
      });

      test('should handle abbreviations in text amounts', () async {
        await learnedService.learn('electronics',
          type: TransactionType.expense,
          categoryId: 'shopping',
          // No confirmed amount
        );

        final context = ParsingContext(text: 'electronics purchase 2.5k');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(2500.0));
      });
    });

    group('Transaction Building', () {
      test('should extract hashtags from text', () async {
        await learnedService.learn('movie',
          type: TransactionType.expense,
          categoryId: 'entertainment',
          confirmedAmount: 50.0,
        );

        final context = ParsingContext(text: 'movie night #weekend #fun #date');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.tags, containsAll(['weekend', 'fun', 'date']));
      });

      test('should set current date for transaction', () async {
        await learnedService.learn('breakfast',
          type: TransactionType.expense,
          categoryId: 'food',
          confirmedAmount: 20.0,
        );

        final beforeTime = DateTime.now();
        final context = ParsingContext(text: 'breakfast');
        final result = await strategy.execute(context);
        final afterTime = DateTime.now();

        expect(result, isNotNull);
        expect(result!.transaction.date.isAfter(beforeTime.subtract(const Duration(seconds: 1))), isTrue);
        expect(result!.transaction.date.isBefore(afterTime.add(const Duration(seconds: 1))), isTrue);
      });

      test('should generate unique transaction IDs', () async {
        await learnedService.learn('snack',
          type: TransactionType.expense,
          categoryId: 'food',
          confirmedAmount: 10.0,
        );

        final context1 = ParsingContext(text: 'snack');
        final context2 = ParsingContext(text: 'snack');

        final result1 = await strategy.execute(context1);
        final result2 = await strategy.execute(context2);

        expect(result1, isNotNull);
        expect(result2, isNotNull);
        expect(result1!.transaction.id, isNot(equals(result2!.transaction.id)));
      });
    });

    group('Error Handling', () {
      test('should handle gracefully when no association exists', () async {
        // Don't learn any association for this text
        final context = ParsingContext(text: 'unknown transaction text');
        final result = await strategy.execute(context);

        expect(result, isNull); // Should decline to handle
      });

      test('should handle empty text gracefully', () async {
        final context = ParsingContext(text: '');
        final result = await strategy.execute(context);

        expect(result, isNull); // Should decline to handle
      });

      test('should handle whitespace-only text gracefully', () async {
        final context = ParsingContext(text: '   \n\t  ');
        final result = await strategy.execute(context);

        expect(result, isNull); // Should decline to handle
      });
    });
  });
}
