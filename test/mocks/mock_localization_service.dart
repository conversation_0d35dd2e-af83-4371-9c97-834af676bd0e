import 'dart:ui';
import '../../lib/services/localization_service.dart';
import '../../lib/models/localization_data.dart';

/// Mock LocalizationService for testing purposes
class MockLocalizationService extends LocalizationService {
  final Map<String, LocalizationData> _mockData = {};
  final Map<String, LocalizationData> _cache = {};
  bool _shouldThrowError = false;
  Duration? _loadDelay;

  MockLocalizationService() : super.forTesting();

  /// Configure the mock to return specific data for a locale
  void setMockData(String localeKey, LocalizationData data) {
    _mockData[localeKey] = data;
  }

  /// Configure the mock to throw errors
  void setShouldThrowError(bool shouldThrow) {
    _shouldThrowError = shouldThrow;
  }

  /// Configure the mock to simulate loading delays
  void setLoadDelay(Duration? delay) {
    _loadDelay = delay;
  }

  /// Set up default English mock data
  void setupDefaultEnglishData() {
    const englishData = LocalizationData(
      locale: 'en-US',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      expenseKeywords: [
        'spent', 'paid', 'bought', 'purchased', 'expense', 'pay', 'cost',
        'spent on', 'paid for', 'charge', 'bought for', 'dinner', 'lunch',
        'breakfast', 'meal', 'food', 'coffee', 'restaurant', 'groceries',
        'shopping', 'gas', 'fuel', 'for'
      ],
      incomeKeywords: [
        'received', 'earned', 'income', 'salary', 'payment received',
        'got paid', 'got money', 'earned from', 'money from', 'receive',
        'selling', 'sold', 'gift', 'bonus', 'dividend', 'interest',
        'return', 'gain', 'profit', 'reward', 'refund'
      ],
      loanKeywords: [
        'borrowed', 'lent', 'loan', 'debt', 'credit', 'lend',
        'borrowed from', 'lent to', 'borrow'
      ],
      currencySymbols: ['\$', '€', '£', '¥', '₹'],
      specialPatterns: {
        'for_keyword': r'\bfor\b',
        'income_before_for': r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b',
        'payment_exclusion': r'pay(?!ment received)',
        'refund_from': r'refund.*from',
      },
    );
    setMockData('en', englishData);
  }

  /// Set up default Spanish mock data
  void setupDefaultSpanishData() {
    const spanishData = LocalizationData(
      locale: 'es-ES',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      expenseKeywords: [
        'gasté', 'pagado', 'comprado', 'costo', 'cena', 'almuerzo',
        'desayuno', 'comida', 'café', 'restaurante', 'compras',
        'gasolina', 'combustible', 'gasto', 'pagué', 'compré',
        'gastado', 'precio', 'caro', 'barato', 'para'
      ],
      incomeKeywords: [
        'recibido', 'ganado', 'salario', 'vendido', 'regalo',
        'ingreso', 'cobrado', 'dinero recibido', 'pago recibido',
        'ganancia', 'beneficio', 'bono', 'dividendo', 'interés',
        'retorno', 'reembolso', 'venta'
      ],
      loanKeywords: [
        'prestado', 'préstamo', 'deuda', 'crédito', 'prestar',
        'prestado de', 'prestado a', 'pedir prestado', 'deber', 'adeudo'
      ],
      currencySymbols: ['\$', '€', '£', '¥', '₹'],
      specialPatterns: {
        'for_keyword': r'\bpara\b',
        'income_before_for': r'(recibido|ganado|salario|vendido|regalo|ingreso|cobrado|dinero recibido|pago recibido).*?\bpara\b',
        'payment_exclusion': r'pag(?!o recibido)',
        'refund_from': r'reembolso.*de',
      },
    );
    setMockData('es', spanishData);
  }

  @override
  Future<LocalizationData> getPatternsForLocale(Locale locale) async {
    if (_shouldThrowError) {
      throw Exception('Mock error: Failed to load localization data');
    }

    if (_loadDelay != null) {
      await Future.delayed(_loadDelay!);
    }

    final localeKey = locale.languageCode;
    
    // Check cache first
    if (_cache.containsKey(localeKey)) {
      return _cache[localeKey]!;
    }

    // Check mock data
    if (_mockData.containsKey(localeKey)) {
      final data = _mockData[localeKey]!;
      _cache[localeKey] = data;
      return data;
    }

    // Fallback to English if available
    if (localeKey != 'en' && _mockData.containsKey('en')) {
      final englishData = _mockData['en']!;
      _cache['en'] = englishData;
      return englishData;
    }

    throw Exception('Mock error: No data available for locale $localeKey');
  }

  @override
  Future<void> preloadLocale(Locale locale) async {
    if (!isLocaleCached(locale)) {
      try {
        await getPatternsForLocale(locale);
      } catch (e) {
        // Preloading failures are not critical in mock
        print('Mock warning: Failed to preload locale ${locale.languageCode}: $e');
      }
    }
  }

  @override
  void clearCache() {
    _cache.clear();
  }

  @override
  int get cacheSize => _cache.length;

  @override
  bool isLocaleCached(Locale locale) {
    return _cache.containsKey(locale.languageCode);
  }

  @override
  List<String> get availableLocales => _cache.keys.toList();

  /// Reset the mock to initial state
  void reset() {
    _mockData.clear();
    _cache.clear();
    _shouldThrowError = false;
    _loadDelay = null;
  }

  /// Get the mock data for testing purposes
  Map<String, LocalizationData> get mockData => Map.unmodifiable(_mockData);

  /// Check if mock has data for a specific locale
  bool hasMockDataFor(String localeKey) {
    return _mockData.containsKey(localeKey);
  }

  /// Simulate cache hit for testing
  void simulateCacheHit(String localeKey, LocalizationData data) {
    _cache[localeKey] = data;
  }

  /// Get cached data for testing
  LocalizationData? getCachedData(String localeKey) {
    return _cache[localeKey];
  }
}
