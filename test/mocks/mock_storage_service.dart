import '../../lib/services/storage_service.dart';

/// Mock implementation of StorageService for testing
/// Uses in-memory storage instead of SharedPreferences
class MockStorageService extends StorageService {
  final Map<String, dynamic> _storage = {};
  bool _isInitialized = false;

  @override
  Future<void> init() async {
    _isInitialized = true;
  }

  bool get isInitialized => _isInitialized;

  // String operations
  @override
  Future<bool> setString(String key, String value) async {
    _storage[key] = value;
    return true;
  }

  @override
  String? getString(String key) {
    return _storage[key] as String?;
  }

  // String list operations
  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _storage[key] = List<String>.from(value);
    return true;
  }

  @override
  List<String>? getStringList(String key) {
    final value = _storage[key];
    return value != null ? List<String>.from(value) : null;
  }

  // Bool operations
  @override
  Future<bool> setBool(String key, bool value) async {
    _storage[key] = value;
    return true;
  }

  @override
  bool? getBool(String key) {
    return _storage[key] as bool?;
  }

  // Int operations
  @override
  Future<bool> setInt(String key, int value) async {
    _storage[key] = value;
    return true;
  }

  @override
  int? getInt(String key) {
    return _storage[key] as int?;
  }

  // Double operations
  @override
  Future<bool> setDouble(String key, double value) async {
    _storage[key] = value;
    return true;
  }

  @override
  double? getDouble(String key) {
    return _storage[key] as double?;
  }

  // Remove a specific key
  @override
  Future<bool> remove(String key) async {
    _storage.remove(key);
    return true;
  }

  // Clear all data
  @override
  Future<bool> clear() async {
    _storage.clear();
    return true;
  }

  // Check if a key exists
  @override
  bool containsKey(String key) {
    return _storage.containsKey(key);
  }

  // Default currency operations
  @override
  Future<void> saveDefaultCurrency(String currencyCode) async {
    await setString('default_currency', currencyCode);
  }

  @override
  Future<String> getDefaultCurrency() async {
    return getString('default_currency') ?? 'USD';
  }

  /// Helper method for testing - get all stored data
  Map<String, dynamic> getAllData() {
    return Map<String, dynamic>.from(_storage);
  }

  /// Helper method for testing - reset storage
  void reset() {
    _storage.clear();
    _isInitialized = false;
  }
}
