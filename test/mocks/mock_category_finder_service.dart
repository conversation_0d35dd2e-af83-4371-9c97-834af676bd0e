import '../../lib/models/transaction_model.dart';
import '../../lib/services/parser/category_finder_service.dart';
import '../mocks/mock_storage_service.dart';

/// Mock implementation of CategoryFinderService for testing purposes.
///
/// This mock follows the established patterns from MockStorageService and
/// MockLocalizationService, providing configurable responses and error simulation.
class MockCategoryFinderService extends CategoryFinderService {
  final Map<String, String> _mockCategories = {};
  String? _defaultCategory;
  bool _shouldThrowError = false;
  String? _errorMessage;

  /// Create mock with a mock storage service
  MockCategoryFinderService() : super(MockStorageService());

  /// Configure the mock to return a specific category for a text
  void setMockCategory(String text, String categoryId) {
    _mockCategories[text.toLowerCase().trim()] = categoryId;
  }

  /// Configure the mock to return a default category when no specific mapping is found
  void setDefaultCategory(String categoryId) {
    _defaultCategory = categoryId;
  }

  /// Configure the mock to throw errors
  void simulateError(bool shouldThrow, [String? errorMessage]) {
    _shouldThrowError = shouldThrow;
    _errorMessage = errorMessage;
  }

  /// Reset the mock to initial state
  void reset() {
    _mockCategories.clear();
    _defaultCategory = null;
    _shouldThrowError = false;
    _errorMessage = null;
  }

  /// Mock implementation of findCategory method
  @override
  Future<String?> findCategory(String text, TransactionType type) async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock CategoryFinderService error');
    }

    final normalizedText = text.toLowerCase().trim();
    
    // Check for specific mock mapping
    if (_mockCategories.containsKey(normalizedText)) {
      return _mockCategories[normalizedText];
    }

    // Check for partial matches (similar to real service behavior)
    for (final entry in _mockCategories.entries) {
      if (normalizedText.contains(entry.key) || entry.key.contains(normalizedText)) {
        return entry.value;
      }
    }

    // Return default category if configured
    return _defaultCategory;
  }

  /// Mock implementation of learnCategory method
  @override
  Future<void> learnCategory(String text, String categoryId) async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock CategoryFinderService error');
    }

    // Store the learned category for future findCategory calls
    setMockCategory(text, categoryId);
  }

  /// Mock implementation of getAllLearnedCategories method
  @override
  Future<Map<String, String>> getAllLearnedCategories() async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock CategoryFinderService error');
    }

    return Map.unmodifiable(_mockCategories);
  }

  /// Mock implementation of clearLearnedData method
  Future<void> clearLearnedData() async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock CategoryFinderService error');
    }

    _mockCategories.clear();
  }

  /// Mock implementation of exportLearnedData method
  Future<String?> exportLearnedData() async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock CategoryFinderService error');
    }

    if (_mockCategories.isEmpty) {
      return null;
    }

    // Simple JSON-like export for testing
    final entries = _mockCategories.entries.map((e) => '"${e.key}": "${e.value}"').join(', ');
    return '{$entries}';
  }

  /// Mock implementation of getAvailableCategoryIds method
  List<String> getAvailableCategoryIds() {
    return ['food', 'transport', 'shopping', 'entertainment', 'health', 'other'];
  }

  /// Mock implementation of getKeywordsForCategory method
  @override
  List<String> getKeywordsForCategory(String categoryId) {
    final mockKeywords = <String, List<String>>{
      'food': ['restaurant', 'coffee', 'lunch', 'dinner', 'grocery'],
      'transport': ['uber', 'taxi', 'bus', 'train', 'gas'],
      'shopping': ['amazon', 'store', 'mall', 'purchase', 'buy'],
      'entertainment': ['movie', 'concert', 'game', 'show', 'ticket'],
      'health': ['doctor', 'pharmacy', 'hospital', 'medicine', 'clinic'],
      'other': <String>[],
    };

    return mockKeywords[categoryId] ?? <String>[];
  }

  /// Helper method to check if mock has data for a specific text
  bool hasMockCategoryFor(String text) {
    return _mockCategories.containsKey(text.toLowerCase().trim());
  }

  /// Helper method to get all mock data for testing
  Map<String, String> get mockData => Map.unmodifiable(_mockCategories);

  /// Helper method to get default category for testing
  String? get defaultCategory => _defaultCategory;

  /// Helper method to check error state for testing
  bool get isErrorSimulated => _shouldThrowError;
}
