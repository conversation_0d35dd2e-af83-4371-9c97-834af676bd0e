import 'dart:ui';
import '../../lib/services/parser/fallback_parser_service.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/transaction_model.dart';
import 'package:uuid/uuid.dart';

/// Mock implementation of FallbackParserService for testing purposes
class MockFallbackParserService extends FallbackParserService {
  bool _shouldThrowError = false;
  String? _errorMessage;
  ParseResult? _mockResult;
  final Uuid _uuid = const Uuid();

  MockFallbackParserService(super.storageService, {super.localizationService});

  /// Configure the mock to throw errors
  void setShouldThrowError(bool shouldThrow, [String? errorMessage]) {
    _shouldThrowError = shouldThrow;
    _errorMessage = errorMessage;
  }

  /// Configure the mock to return a specific result
  void setMockResult(ParseResult result) {
    _mockResult = result;
  }

  /// Reset the mock to initial state
  void reset() {
    _shouldThrowError = false;
    _errorMessage = null;
    _mockResult = null;
  }

  @override
  Future<ParseResult> parseTransaction(String text, {Locale? locale}) async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock FallbackParserService error');
    }

    if (_mockResult != null) {
      return _mockResult!;
    }

    // Fallback to parent implementation if no mock behavior is configured
    return super.parseTransaction(text, locale: locale);
  }

  /// Helper method to create a mock successful result
  ParseResult createMockSuccessResult(String text, {
    double amount = 10.0,
    TransactionType type = TransactionType.expense,
    String categoryId = 'food',
    String currencyCode = 'USD',
  }) {
    final transaction = Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: type,
      categoryId: categoryId,
      date: DateTime.now(),
      description: text.trim(),
      tags: [],
      currencyCode: currencyCode,
    );
    return ParseResult.success(transaction);
  }

  /// Helper method to create a mock failed result
  ParseResult createMockFailedResult(String text, {
    String errorMessage = 'Mock parsing failed',
    String currencyCode = 'USD',
  }) {
    final transaction = Transaction(
      id: _uuid.v4(),
      amount: 0.0,
      type: TransactionType.expense,
      categoryId: 'unknown',
      date: DateTime.now(),
      description: text.trim(),
      tags: [],
      currencyCode: currencyCode,
    );
    return ParseResult.failed(transaction, errorMessage);
  }
}
